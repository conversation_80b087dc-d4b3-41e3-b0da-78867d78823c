package com.taktile.client

import co.highbeam.metrics.Metrics
import co.highbeam.protectedString.ProtectedString
import io.micrometer.core.instrument.logging.LoggingMeterRegistry
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

class TaktileClientTest {

  @Test
  fun `test flow versions endpoint`() = runBlocking {
    val metrics = Metrics(LoggingMeterRegistry())
    val httpClient = TaktileHttpClient(
      environment = TaktileHttpClient.Environment.Sandbox,
      metrics = metrics
    )

    val client = TaktileClient(
      httpClient = httpClient,
      taktileApiKey = ProtectedString("30fbeb7f-7064-4032-b926-4ce951285db4"),
      environment = TaktileHttpClient.Environment.Sandbox
    )

    try {
      val versions = client.getFlowVersions("middesk-kyb-flow")
      println("Flow versions response: $versions")
    } catch (e: Exception) {
      println("Error getting flow versions: ${e.message}")
      e.printStackTrace()
    }
  }

  @Test
  fun `test decide endpoint with actual application payload`() = runBlocking {
    val metrics = Metrics(LoggingMeterRegistry())
    val httpClient = TaktileHttpClient(
      environment = TaktileHttpClient.Environment.Sandbox,
      metrics = metrics
    )

    val client = TaktileClient(
      httpClient = httpClient,
      taktileApiKey = ProtectedString("30fbeb7f-7064-4032-b926-4ce951285db4"),
      environment = TaktileHttpClient.Environment.Sandbox
    )

    // Create a request that matches exactly what the application is sending
    val actualPayload = """
      {
        "data": {
          "business_guid": "822ce1c7-bfad-4f3e-bd13-f7da47dc88aa",
          "capital_application_guid": "0b747cfe-758c-4d2c-8de4-816f381d6af6",
          "business_name": "Capital application 3",
          "business_dba": null,
          "phone_number": "*************",
          "ein": "123123123",
          "incorporation_state": "AZ",
          "associated_person": "Justin McKibben",
          "address": {
            "line1": "123 Melrose St",
            "line2": "",
            "city": "New York",
            "state": "NY",
            "postal_code": "11206",
            "country": "US"
          }
        },
        "metadata": {
          "version": "1.0",
          "entity_id": "822ce1c7-bfad-4f3e-bd13-f7da47dc88aa"
        },
        "control": {
          "execution_mode": "async"
        }
      }
    """.trimIndent()

    try {
      val response = client.request.request(
        httpMethod = io.ktor.http.HttpMethod.Post,
        path = "/run/api/v1/flows/middesk-kyb-flow/sandbox/decide",
        body = actualPayload
      )
      println("Actual payload request response: ${response.statusCode}")
      println("Response body: ${response.readResponseBody()}")
    } catch (e: Exception) {
      println("Error with actual payload request: ${e.message}")
      e.printStackTrace()
    }
  }

  @Test
  fun `test decide endpoint with minimal payload like curl`() = runBlocking {
    val metrics = Metrics(LoggingMeterRegistry())
    val httpClient = TaktileHttpClient(
      environment = TaktileHttpClient.Environment.Sandbox,
      metrics = metrics
    )

    val client = TaktileClient(
      httpClient = httpClient,
      taktileApiKey = ProtectedString("30fbeb7f-7064-4032-b926-4ce951285db4"),
      environment = TaktileHttpClient.Environment.Sandbox
    )

    // Create a minimal request similar to the working curl command
    val rawJson = """
      {
        "data": {
          "business_guid": "822ce1c7-bfad-4f3e-bd13-f7da47dc88aa",
          "capital_application_guid": "0e7f1c64-fd11-41c1-9a64-b03559e14f33",
          "business_name": "Capital application 3",
          "business_dba": "",
          "ein": "123123123",
          "address": {
            "line1": "123 Melrose St",
            "line2": "",
            "city": "New York",
            "state": "NY",
            "postalCode": "11206",
            "country": "US"
          },
          "associated_person": "Justin McKibben"
        },
        "metadata": {
          "version": "v1.0",
          "entity_id": "822ce1c7-bfad-4f3e-bd13-f7da47dc88aa"
        },
        "control": {
          "execution_mode": "async"
        }
      }
    """.trimIndent()

    try {
      val response = client.request.request(
        httpMethod = io.ktor.http.HttpMethod.Post,
        path = "/run/api/v1/flows/middesk-kyb-flow/sandbox/decide",
        body = rawJson
      )
      println("Raw JSON request response: ${response.statusCode}")
      println("Response body: ${response.readResponseBody()}")
    } catch (e: Exception) {
      println("Error with raw JSON request: ${e.message}")
      e.printStackTrace()
    }
  }
}
