package com.taktile.client

import co.highbeam.metrics.Metrics
import co.highbeam.protectedString.ProtectedString
import io.micrometer.core.instrument.logging.LoggingMeterRegistry
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

class TaktileClientTest {
  
  @Test
  fun `test flow versions endpoint`() = runBlocking {
    val metrics = Metrics(LoggingMeterRegistry())
    val httpClient = TaktileHttpClient(
      environment = TaktileHttpClient.Environment.Sandbox,
      metrics = metrics
    )
    
    val client = TaktileClient(
      httpClient = httpClient,
      taktileApiKey = ProtectedString("30fbeb7f-7064-4032-b926-4ce951285db4"),
      environment = TaktileHttpClient.Environment.Sandbox
    )
    
    try {
      val versions = client.getFlowVersions("middesk-kyb-flow")
      println("Flow versions response: $versions")
    } catch (e: Exception) {
      println("Error getting flow versions: ${e.message}")
      e.printStackTrace()
    }
  }
}
