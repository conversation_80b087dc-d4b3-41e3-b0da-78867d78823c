package com.taktile.client

import co.highbeam.client.HighbeamHttpClientRequestBuilder
import co.highbeam.client.HttpResponse
import co.highbeam.client.RequestBuilder
import co.highbeam.protectedString.ProtectedString
import io.ktor.http.HttpMethod
import mu.KotlinLogging

internal class TaktileRequest(
  private val httpClient: TaktileHttpClient,
  private val apiKey: ProtectedString,
) {
  private val logger = KotlinLogging.logger {}

  suspend fun request(
    httpMethod: HttpMethod,
    path: String,
    qp: Map<String, List<String>> = emptyMap(),
    body: Any? = null,
    token: RequestBuilder = { useTaktileApiKey() },
  ): HttpResponse {
    logger.info { "Taktile request: [httpMethod=$httpMethod, path=$path, qp=$qp]" }

    // Log the actual serialized JSON that will be sent to Taktile
    val serializedBody = when (body) {
      null -> "null"
      is String -> body
      else -> httpClient.objectMapper.writeValueAsString(body)
    }
    logger.info { "Taktile request body (serialized JSON): $serializedBody" }

    val response = httpClient.request(
      httpMethod = httpMethod,
      path = path,
      qp = qp,
      body = body,
      builder = token,
    )

    // Debug logging for response details
    logger.info { "Taktile response: [statusCode=${response.statusCode}, headers=${response.headers.entries().map { "${it.key}: ${it.value}" }}]" }

    // Log response body for debugging (be careful with sensitive data)
    try {
      val responseBody = response.readResponseBody()
      logger.info { "Taktile response body: $responseBody" }
    } catch (e: Exception) {
      logger.warn { "Failed to read response body for logging: ${e.message}" }
    }

    return response
  }

  private fun HighbeamHttpClientRequestBuilder.useTaktileApiKey() {
    putHeader("X-Api-Key", apiKey.value)
  }
}
