package com.taktile.client

import co.highbeam.protectedString.ProtectedString
import com.taktile.rep.DecisionRequest
import com.taktile.rep.DecisionResponse
import io.ktor.http.HttpMethod

class TaktileClient(
  httpClient: TaktileHttpClient,
  taktileApiKey: ProtectedString,
  val environment: TaktileHttpClient.Environment,
) {
  internal val request: TaktileRequest = TaktileRequest(
    httpClient,
    taktileApiKey,
  )

  suspend fun <T> decide(
    flowSlug: String,
    decisionRequest: DecisionRequest<T>
  ): DecisionResponse? {
    return request.request(
      httpMethod = HttpMethod.Post,
      path = when (environment) {
        TaktileHttpClient.Environment.Sandbox -> "/run/api/v1/flows/$flowSlug/sandbox/decide"
        TaktileHttpClient.Environment.Production -> "/run/api/v1/flows/$flowSlug/decide"
      },
      body = decisionRequest
    ).readValue()
  }

  suspend fun getFlowVersions(flowSlug: String): String? {
    return request.request(
      httpMethod = HttpMethod.Get,
      path = "/run/api/v1/flows/$flowSlug/versions"
    ).readText()
  }
}
